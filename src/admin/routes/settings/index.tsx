import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Cog6Tooth } from "@camped-ai/icons";
import HideSidebarItemsWidget from "../../widgets/hide-sidebar-items-widget";
import "../../utils/navigation-aware-sidebar-hider";
import "../../utils/robust-page-load-hider";

const SettingsPage = () => {
  return (
    <>
      <HideSidebarItemsWidget />
      {/* The actual settings content will be rendered by Medusa's default settings page */}
    </>
  );
};

export const config = defineRouteConfig({
  label: "Settings",
  icon: Cog6Tooth,
});

export default SettingsPage;
