import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules, ContainerRegistrationKeys } from "@camped-ai/framework/utils";

/**
 * Public endpoint to list all available hotels
 *
 * @param req - The request object
 * @param res - The response object
 * @returns A list of available hotels with their lowest room prices
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const pricingModuleService = req.scope.resolve(Modules.PRICING);

    // Get query parameters with defaults
    const {
      currency_code = "USD",
      limit = 20,
      offset = 0,
      is_featured,
      is_active,
      is_pets_allowed,
      searchParams,
    } = req.query;

    // Build filters based on query parameters
    const filters: Record<string, any> = {};
    const params =
      searchParams && typeof searchParams === "string"
        ? JSON.parse(searchParams)
        : null;

    // Add is_featured filter if provided
    if (is_featured !== undefined) {
      filters.is_featured = is_featured === "true";
    }

    filters.is_active = true;

    console.log("Applying filters:", JSON.stringify(filters));

    // Add is_pets_allowed filter if provided
    if (is_pets_allowed !== undefined) {
      filters.is_pets_allowed = is_pets_allowed === "true";
    }

    // Add destination filter if provided in searchParams
    if (params && params?.destination_id) {
      filters.destination_id = params?.destination_id;
    }

    console.log("Applying filters:", JSON.stringify(filters));

    // Get all hotels directly from the hotel entity with filters
    const {
      data: hotels,
      metadata: { count, take, skip },
    } = await query.graph({
      entity: "hotel",
      filters: filters,
      fields: [
        "id",
        "name",
        "handle",
        "category_id",
        "description",
        "rating",
        "address",
        "images.*",
        "is_featured",
        "is_active",
        "is_pets_allowed",
        "destination_id",
        "amenities",
        "rules",
        "location",
        "safety_measures",
      ],
      pagination: {
        skip: Number(offset),
        take: Number(limit),
      },
    });

    if (!hotels || hotels.length === 0) {
      return res.json({
        hotels: [],
        count: 0,
      });
    }

    // Get all products
    const { data: allProducts } = await query.graph({
      entity: "product",
      filters: {},
      fields: ["id", "title", "metadata"],
      pagination: {
        skip: 0,
        take: 1000, // Increased limit to get more products
      },
    });

    // Filter products that have price_set_id in their metadata
    const productsWithPriceSet = allProducts.filter(
      (product: any) => product.metadata && product.metadata.price_set_id
    );

    if (productsWithPriceSet.length === 0) {
      // Return hotels without products
      const hotelsWithoutProducts = hotels.map((hotel) => ({
        ...hotel,
        products: [],
        lowest_price: null,
      }));

      return res.json({
        hotels: hotelsWithoutProducts,
        count: hotelsWithoutProducts.length,
      });
    }

    // Group products by hotel_id in metadata
    const productsByHotelId: Record<string, any[]> = {};

    productsWithPriceSet.forEach((product: any) => {
      const hotelId = product.metadata?.hotel_id;

      if (hotelId) {
        if (!productsByHotelId[hotelId]) {
          productsByHotelId[hotelId] = [];
        }

        productsByHotelId[hotelId].push(product);
      }
    });

    // Process each hotel to find the lowest price
    const hotelsWithProducts = await Promise.all(
      hotels.map(async (hotel) => {
        const hotelProducts = productsByHotelId[hotel.id] || [];

        // Log hotel and its products count
        console.log(
          `Hotel: ${hotel.id} (${hotel.name}) (${hotel.location}) - Products: ${hotelProducts.length}`
        );

        // If no products, return hotel with null prices
        if (hotelProducts.length === 0) {
          return {
            ...hotel,
            lowest_price: null,
            highest_price: null,
          };
        }

        // Get prices for all price sets
        let lowestPrice = null;
        let highestPrice = null;

        try {
          // Get prices for each product using the pricing module
          for (const product of hotelProducts) {
            const priceSetId = product.metadata?.price_set_id;

            if (!priceSetId) {
              continue;
            }

            try {
              // Get all prices for this price set
              const allPrices = await pricingModuleService.listPrices(
                {
                  price_set_id: [priceSetId],
                  currency_code: currency_code as string,
                },
                { select: ["*"] }
              );

              // Filter prices to only include those with exactly "base-none-1-1" title
              // AND where price_list_id is null
              const exactPrices = allPrices.filter(
                (price) =>
                  price.title === "base-none-1-1" &&
                  (!price.price_list || !price.price_list.id)
              );

              // Log the relationship: hotel_id -> product_id -> price_set_id -> price
              if (exactPrices.length > 0) {
                exactPrices.forEach((price) => {
                  console.log(
                    `${hotel.id} -> ${product.id} -> ${priceSetId} -> ${price.amount}`
                  );

                  // Update lowest price if this is the first price or if it's lower than the current lowest price
                  if (
                    lowestPrice === null ||
                    price.amount < lowestPrice.amount
                  ) {
                    lowestPrice = {
                      amount: price.amount,
                      original_amount: price.amount,
                      currency_code: price.currency_code,
                      per_night_amount: price.amount,
                      product_id: product.id,
                      product_title: product.title,
                    };
                  }

                  // Update highest price if this is the first price or if it's higher than the current highest price
                  if (
                    highestPrice === null ||
                    price.amount > highestPrice.amount
                  ) {
                    highestPrice = {
                      amount: price.amount,
                      original_amount: price.amount,
                      currency_code: price.currency_code,
                      per_night_amount: price.amount,
                      product_id: product.id,
                      product_title: product.title,
                    };
                  }
                });
              }
            } catch (error) {
              console.error(
                `Error getting prices for hotel ${hotel.id} (${hotel.name}), product ${product.id} (${product.title}), price set ${priceSetId}:`,
                error.message
              );
            }
          }
        } catch (error) {
          console.error(
            `Error processing hotel ${hotel.id} (${hotel.name}):`,
            error.message
          );
        }

        // No default price, just use null if no price was found

        // Log summary for this hotel
        console.log(
          `Hotel ${hotel.id}: Lowest price: ${
            lowestPrice?.amount || "None"
          }, Highest price: ${highestPrice?.amount || "None"}`
        );

        // Return hotel with simplified lowest and highest prices (just the amount)
        return {
          ...hotel,
          lowest_price: lowestPrice ? lowestPrice.amount : null,
          highest_price: highestPrice ? highestPrice.amount : null,
        };
      })
    );

    // Return the results
    return res.json({
      hotels: hotelsWithProducts,
      count,
      limit: take,
      offset: skip,
    });
  } catch (error) {
    console.error("Error listing hotels:", error);
    return res.status(500).json({
      message: "Error listing hotels",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
