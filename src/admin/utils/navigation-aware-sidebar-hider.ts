/**
 * Navigation-aware sidebar item hider
 * This utility ensures sidebar items are hidden on all navigation events
 */

import { HIDDEN_SIDEBAR_ITEMS } from "../config/sidebar-config";

let isInitialized = false;
let observer: MutationObserver | null = null;

/**
 * Hide sidebar items immediately
 */
const hideSidebarItems = () => {
  const normalizedItems = HIDDEN_SIDEBAR_ITEMS.map(item => item.toLowerCase());
  
  // Find all navigation links
  const selectors = [
    'nav a',
    'aside a', 
    '[role="navigation"] a',
    '.sidebar a',
    '.navigation a'
  ];
  
  selectors.forEach(selector => {
    try {
      const links = document.querySelectorAll(selector);
      
      links.forEach(link => {
        const text = link.textContent?.trim().toLowerCase() || '';
        
        if (normalizedItems.some(item => text === item || text.includes(item))) {
          // Hide the entire list item if it exists, otherwise hide the link
          const listItem = link.closest('li');
          const container = link.closest('div');
          
          if (listItem) {
            listItem.style.display = 'none';
            listItem.style.height = '0';
            listItem.style.margin = '0';
            listItem.style.padding = '0';
            listItem.style.overflow = 'hidden';
          } else if (container && container !== document.body) {
            container.style.display = 'none';
            container.style.height = '0';
            container.style.margin = '0';
            container.style.padding = '0';
            container.style.overflow = 'hidden';
          } else {
            link.style.display = 'none';
          }
        }
      });
    } catch (error) {
      console.error('[NavigationAwareSidebarHider] Error with selector:', selector, error);
    }
  });
};

/**
 * Set up navigation listeners
 */
const setupNavigationListeners = () => {
  // Listen for popstate (back/forward navigation)
  window.addEventListener('popstate', () => {
    setTimeout(hideSidebarItems, 50);
  });
  
  // Override history methods to catch programmatic navigation
  const originalPushState = history.pushState;
  const originalReplaceState = history.replaceState;
  
  history.pushState = function(...args) {
    originalPushState.apply(history, args);
    setTimeout(hideSidebarItems, 50);
  };
  
  history.replaceState = function(...args) {
    originalReplaceState.apply(history, args);
    setTimeout(hideSidebarItems, 50);
  };
  
  // Set up MutationObserver for dynamic content
  if (observer) {
    observer.disconnect();
  }
  
  observer = new MutationObserver(() => {
    hideSidebarItems();
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: false,
    characterData: false
  });
};

/**
 * Initialize the sidebar hider
 */
export const initNavigationAwareSidebarHider = () => {
  if (isInitialized) {
    return;
  }
  
  isInitialized = true;
  
  // Hide items immediately
  hideSidebarItems();
  
  // Set up navigation listeners
  setupNavigationListeners();
  
  // Set up periodic checks as fallback
  setInterval(hideSidebarItems, 2000);
  
  // Run after delays to catch late-loading content
  setTimeout(hideSidebarItems, 100);
  setTimeout(hideSidebarItems, 500);
  setTimeout(hideSidebarItems, 1000);
  setTimeout(hideSidebarItems, 2000);
};

/**
 * Auto-initialize when this module is imported
 */
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initNavigationAwareSidebarHider);
  } else {
    initNavigationAwareSidebarHider();
  }
  
  window.addEventListener('load', initNavigationAwareSidebarHider);
}

export { hideSidebarItems };
