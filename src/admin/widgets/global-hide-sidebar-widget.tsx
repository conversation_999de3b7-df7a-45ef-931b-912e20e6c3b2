import { defineWidgetConfig } from "@camped-ai/admin-sdk";
import { useEffect } from "react";
import { HIDDEN_SIDEBAR_ITEMS } from "../config/sidebar-config";

/**
 * Global widget that runs on all pages to hide sidebar items
 * This ensures sidebar items are hidden regardless of navigation method
 */
const GlobalHideSidebarWidget = () => {
  useEffect(() => {
    // Function to hide sidebar items
    const hideSidebarItems = () => {
      const normalizedItemsToHide = HIDDEN_SIDEBAR_ITEMS.map(item => item.toLowerCase());
      
      // Find all navigation links
      const allLinks = document.querySelectorAll('nav a, aside a, [role="navigation"] a');
      
      allLinks.forEach(link => {
        const linkText = link.textContent?.trim().toLowerCase() || '';
        
        const shouldHide = normalizedItemsToHide.some(hideText => {
          return linkText === hideText || linkText.includes(hideText);
        });
        
        if (shouldHide) {
          // Hide the entire list item if it exists, otherwise hide the link
          const listItem = link.closest('li');
          if (listItem) {
            listItem.style.display = 'none';
          } else {
            link.style.display = 'none';
          }
        }
      });
    };

    // Run immediately
    hideSidebarItems();

    // Set up MutationObserver to handle dynamic content
    const observer = new MutationObserver(() => {
      hideSidebarItems();
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // Also run on URL changes
    const handleNavigation = () => {
      setTimeout(hideSidebarItems, 100);
    };

    // Listen for navigation events
    window.addEventListener('popstate', handleNavigation);
    
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;
    
    history.pushState = function(...args) {
      originalPushState.apply(history, args);
      handleNavigation();
    };
    
    history.replaceState = function(...args) {
      originalReplaceState.apply(history, args);
      handleNavigation();
    };

    // Cleanup
    return () => {
      observer.disconnect();
      window.removeEventListener('popstate', handleNavigation);
      history.pushState = originalPushState;
      history.replaceState = originalReplaceState;
    };
  }, []);

  return null;
};

export const config = defineWidgetConfig({
  zone: "layout.before", // This zone runs on all pages
});

export default GlobalHideSidebarWidget;
