import React from "react";
import { Select, Label, Text } from "@camped-ai/ui";
import { getLanguageOptions, getLanguageByCode, DEFAULT_LANGUAGE } from "../../config/languages";

interface LanguageSelectorProps {
  value: string;
  onChange: (languageCode: string) => void;
  label?: string;
  id?: string;
  className?: string;
  disabled?: boolean;
  showLabel?: boolean;
  placeholder?: string;
  availableLanguages?: string[]; // Filter to only show specific languages
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  value,
  onChange,
  label = "Language",
  id = "language",
  className = "",
  disabled = false,
  showLabel = true,
  placeholder = "Select language",
  availableLanguages
}) => {
  const allLanguageOptions = getLanguageOptions();
  
  // Filter options if availableLanguages is provided
  const languageOptions = availableLanguages 
    ? allLanguageOptions.filter(option => availableLanguages.includes(option.value))
    : allLanguageOptions;

  // Use default language if no value is provided
  const currentValue = value || DEFAULT_LANGUAGE;
  const selectedLanguage = getLanguageByCode(currentValue);

  return (
    <div className={className}>
      {showLabel && (
        <Label htmlFor={id} className="mb-1 block">
          {label}
        </Label>
      )}
      <Select value={currentValue} onValueChange={onChange} disabled={disabled}>
        <Select.Trigger id={id}>
          <Select.Value placeholder={placeholder}>
            {selectedLanguage && (
              <span className="flex items-center gap-2">
                <span>{selectedLanguage.flag}</span>
                <span>{selectedLanguage.name}</span>
              </span>
            )}
          </Select.Value>
        </Select.Trigger>
        <Select.Content>
          {languageOptions.map((option) => {
            const language = getLanguageByCode(option.value);
            return (
              <Select.Item key={option.value} value={option.value}>
                <span className="flex items-center gap-2">
                  <span>{language?.flag}</span>
                  <span>{language?.name}</span>
                  <span className="text-gray-500">({language?.nativeName})</span>
                </span>
              </Select.Item>
            );
          })}
        </Select.Content>
      </Select>
    </div>
  );
};
