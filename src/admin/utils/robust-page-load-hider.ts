/**
 * Robust page load hider
 * This ensures sidebar items stay hidden on page refresh and navigation
 */

import { runEnhancedHiding } from "../config/sidebar-config";

let isInitialized = false;
let hideInterval: NodeJS.Timeout | null = null;

/**
 * Run hiding logic with multiple attempts
 */
const runHidingWithRetries = () => {
  console.log('[RobustPageLoadHider] Running hiding with retries');
  
  let attempts = 0;
  const maxAttempts = 10;
  
  const attemptHiding = () => {
    attempts++;
    console.log(`[RobustPageLoadHider] Attempt ${attempts}/${maxAttempts}`);
    
    const result = runEnhancedHiding();
    const totalHidden = result.sidebarItemsHidden + result.groupHeadingsHidden;
    
    if (totalHidden > 0 || attempts >= maxAttempts) {
      console.log(`[RobustPageLoadHider] Completed after ${attempts} attempts. Hidden: ${totalHidden} items`);
      return;
    }
    
    // Try again after a short delay
    setTimeout(attemptHiding, 200);
  };
  
  attemptHiding();
};

/**
 * Set up comprehensive event listeners
 */
const setupEventListeners = () => {
  console.log('[RobustPageLoadHider] Setting up event listeners');
  
  // Page load events
  window.addEventListener('load', runHidingWithRetries);
  window.addEventListener('DOMContentLoaded', runHidingWithRetries);
  
  // Navigation events
  window.addEventListener('popstate', () => {
    setTimeout(runHidingWithRetries, 100);
  });
  
  // Override history methods
  const originalPushState = history.pushState;
  const originalReplaceState = history.replaceState;
  
  history.pushState = function(...args) {
    originalPushState.apply(history, args);
    setTimeout(runHidingWithRetries, 100);
  };
  
  history.replaceState = function(...args) {
    originalReplaceState.apply(history, args);
    setTimeout(runHidingWithRetries, 100);
  };
  
  // Set up MutationObserver for dynamic content
  const observer = new MutationObserver((mutations) => {
    let shouldRun = false;
    
    mutations.forEach(mutation => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            // Check if new navigation elements were added
            if (element.querySelector && (
              element.querySelector('nav') || 
              element.querySelector('aside') ||
              element.querySelector('[role="navigation"]') ||
              element.matches('nav, aside, [role="navigation"]')
            )) {
              shouldRun = true;
            }
          }
        });
      }
    });
    
    if (shouldRun) {
      setTimeout(runHidingWithRetries, 100);
    }
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: false,
    characterData: false
  });
  
  // Set up periodic checks as fallback
  if (hideInterval) {
    clearInterval(hideInterval);
  }
  
  hideInterval = setInterval(() => {
    runEnhancedHiding();
  }, 3000);
};

/**
 * Initialize the robust page load hider
 */
export const initRobustPageLoadHider = () => {
  if (isInitialized) {
    return;
  }
  
  isInitialized = true;
  
  console.log('[RobustPageLoadHider] Initializing robust page load hider');
  
  // Run immediately
  runHidingWithRetries();
  
  // Set up event listeners
  setupEventListeners();
  
  // Run after various delays to catch different loading scenarios
  setTimeout(runHidingWithRetries, 100);
  setTimeout(runHidingWithRetries, 500);
  setTimeout(runHidingWithRetries, 1000);
  setTimeout(runHidingWithRetries, 2000);
  setTimeout(runHidingWithRetries, 5000);
};

/**
 * Cleanup function
 */
export const cleanupRobustPageLoadHider = () => {
  if (hideInterval) {
    clearInterval(hideInterval);
    hideInterval = null;
  }
  isInitialized = false;
};

/**
 * Auto-initialize when this module is imported
 */
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initRobustPageLoadHider);
  } else {
    initRobustPageLoadHider();
  }
  
  window.addEventListener('load', initRobustPageLoadHider);
  
  // Also run on focus (when user comes back to the tab)
  window.addEventListener('focus', runHidingWithRetries);
  
  // Run on visibility change
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
      setTimeout(runHidingWithRetries, 100);
    }
  });
}

export { runHidingWithRetries };
