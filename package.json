{"name": "medusa-starter-default", "version": "0.0.1", "description": "A starter for Medusa projects.", "author": "Medusa (https://medusajs.com)", "license": "MIT", "keywords": ["sqlite", "postgres", "typescript", "ecommerce", "headless", "medusa"], "scripts": {"build": "medusa build", "seed": "medusa exec ./src/scripts/seed.ts", "start": "medusa start", "dev": "medusa develop", "test:integration:http": "TEST_TYPE=integration:http NODE_OPTIONS=--experimental-vm-modules jest --silent=false --runInBand --forceExit", "test:integration:modules": "TEST_TYPE=integration:modules NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit", "test:unit": "TEST_TYPE=unit NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit"}, "dependencies": {"@camped-ai/admin-bundler": "2.7.3", "@camped-ai/admin-sdk": "2.7.3", "@camped-ai/cli": "2.7.3", "@camped-ai/framework": "2.7.3", "@camped-ai/icons": "2.7.3", "@camped-ai/medusa": "2.7.3", "@camped-ai/ui": "4.0.9", "@camped-ai/workflows-sdk": "2.7.3", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@google-analytics/data": "^4.12.0", "@headlessui/tailwindcss": "^0.2.2", "@incresco/medusa-plugin-wishlist": "^0.0.6", "@internationalized/date": "^3.7.0", "@mikro-orm/core": "6.4.3", "@mikro-orm/knex": "6.4.3", "@mikro-orm/migrations": "6.4.3", "@mikro-orm/postgresql": "6.4.3", "@mui/material": "^6.1.2", "@nanostores/react": "^0.8.4", "@rsc-labs/medusa-store-analytics-v2": "0.1.0", "@types/html-pdf": "^3.0.3", "awilix": "8.0.1", "cors": "^2.8.5", "csv-writer": "^1.6.0", "date-fns": "3.6.0", "exceljs": "^4.4.0", "handlebars": "^4.7.8", "html-pdf": "^3.0.1", "lucide-react": "^0.475.0", "moment": "^2.30.1", "multer": "^1.4.5-lts.2", "nanostores": "^0.11.4", "nodemailer": "^6.9.16", "pdfkit": "^0.15.1", "pg": "^8.13.0", "razorpay": "^2.9.5", "react-datepicker": "^8.1.0", "react-day-picker": "8.10.1", "react-hook-form": "^7.53.0", "recharts": "^2.15.1", "typeorm": "^0.3.22", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@camped-ai/test-utils": "2.4.6", "@mikro-orm/cli": "6.4.3", "@swc/core": "1.5.7", "@swc/jest": "^0.2.36", "@tailwindcss/forms": "^0.5.10", "@types/jest": "^29.5.13", "@types/node": "^20.0.0", "@types/react": "^18.3.2", "@types/react-dom": "^18.2.25", "autoprefixer": "^10.4.20", "jest": "^29.7.0", "postcss": "^8.5.2", "prop-types": "^15.8.1", "tailwindcss": "3.4.17", "ts-node": "^10.9.2", "typescript": "^5.6.2", "vite": "^5.2.11"}, "engines": {"node": ">=18"}, "packageManager": "yarn@1.22.22"}