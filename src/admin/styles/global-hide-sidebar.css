/**
 * Global CSS to hide sidebar items immediately on page load
 * This prevents any flash of unwanted content
 */

nav a[href*="products"],
nav a[href*="customers"],
nav a[href*="price-lists"],
nav a[href*="promotions"],
nav a[href*="inventory"],
nav a[href*="orders"],
nav a[href*="search"],
nav a[href*="return-reasons"],
nav a[href*="sales-channels"],
nav a[href*="product-types"],
nav a[href*="product-tags"],
nav a[href*="locations"],
nav a[href*="shipping"],
nav a[href*="publishable-api-keys"],
nav a[href*="secret-api-keys"],
nav a[href*="workflows"],
nav a[href*="webhooks"] {
  display: none !important;
}

/* Hide by text content - more aggressive approach */
nav li:has(a span:contains("Return Reasons")),
nav li:has(a span:contains("Sales Channels")),
nav li:has(a span:contains("Product Types")),
nav li:has(a span:contains("Product Tags")),
nav li:has(a span:contains("Tax Regions")),
nav li:has(a span:contains("Regions")),
nav li:has(a span:contains("Locations & Shipping")),
nav li:has(a span:contains("Publishable API Keys")),
nav li:has(a span:contains("Secret API Keys")),
nav li:has(a span:contains("Workflows")) {
  display: none !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
}

/* Alternative selectors for different DOM structures */
aside li:has(a span:contains("Return Reasons")),
aside li:has(a span:contains("Sales Channels")),
aside li:has(a span:contains("Product Types")),
aside li:has(a span:contains("Product Tags")),
aside li:has(a span:contains("Tax Regions")),
aside li:has(a span:contains("Regions")),
aside li:has(a span:contains("Locations & Shipping")),
aside li:has(a span:contains("Publishable API Keys")),
aside li:has(a span:contains("Secret API Keys")),
aside li:has(a span:contains("Workflows")) {
  display: none !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
}

/* Target any navigation container */
[role="navigation"] li:has(a span:contains("Return Reasons")),
[role="navigation"] li:has(a span:contains("Sales Channels")),
[role="navigation"] li:has(a span:contains("Product Types")),
[role="navigation"] li:has(a span:contains("Product Tags")),
[role="navigation"] li:has(a span:contains("Tax Regions")),
[role="navigation"] li:has(a span:contains("Regions")),
[role="navigation"] li:has(a span:contains("Locations & Shipping")),
[role="navigation"] li:has(a span:contains("Publishable API Keys")),
[role="navigation"] li:has(a span:contains("Secret API Keys")),
[role="navigation"] li:has(a span:contains("Workflows")) {
  display: none !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
}

/* Fallback for browsers that don't support :has() */
@supports not (selector(:has(*))) {
  /* JavaScript will handle this case */
  .hide-sidebar-item {
    display: none !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
  }
}
