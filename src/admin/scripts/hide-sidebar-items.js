/**
 * Direct DOM manipulation script to hide sidebar items
 * This script will run as soon as it's loaded, without relying on React's lifecycle
 */

// Items to hide - update this array as needed
const ITEMS_TO_HIDE = [
  // Main navigation items to hide
  "Products",
  "Customers",
  "Price Lists",
  "Promotions",
  "Inventory",
  "Orders",
  "Search",
  // Settings sidebar items to hide
  "Return Reasons",
  "Sales Channels",
  "Product Types",
  "Product Tags",
  // "Users", // Commented out to show Users sidebar item
  "Locations & Shipping",
  // Developer-related items
  "Publishable API Keys",
  "Secret API Keys",
  "Workflows",
  "Webhooks" // If present
];

// Enable debug mode
const DEBUG = true;

// Function to log debug messages
function log(message, ...args) {
  if (DEBUG) {
    console.log(`[HideSidebarItems] ${message}`, ...args);
  }
}

// Function to hide sidebar items
function hideSidebarItems() {
  log("Running sidebar item hiding script");

  // Convert all items to lowercase for case-insensitive comparison
  const normalizedItemsToHide = ITEMS_TO_HIDE.map(item => item.toLowerCase());

  // Function to check and hide sidebar items
  function checkAndHideSidebarItems() {
    log("Checking for sidebar items to hide");

    // Try to find all links in the document
    const allLinks = document.querySelectorAll('a');
    log(`Found ${allLinks.length} links in the document`);

    let hiddenCount = 0;

    // Check each link
    allLinks.forEach(link => {
      try {
        // Get the text content of the link
        const linkText = link.textContent?.trim().toLowerCase() || '';

        // Check if this link should be hidden
        const shouldHide = normalizedItemsToHide.some(hideText => {
          return linkText === hideText || linkText.includes(hideText);
        });

        if (shouldHide) {
          // Hide the link
          link.style.display = 'none';
          hiddenCount++;
          log(`Hidden link with text: "${linkText}"`);
        }
      } catch (error) {
        console.error('[HideSidebarItems] Error processing link:', error);
      }
    });

    log(`Total items hidden: ${hiddenCount}`);
  }

  // Run immediately
  checkAndHideSidebarItems();

  // Set up a MutationObserver to handle dynamically loaded content
  const observer = new MutationObserver((mutations) => {
    // Check if any of the mutations involve links or navigation
    const shouldCheck = mutations.some(mutation => {
      return mutation.target.nodeName === 'NAV' ||
        mutation.target.nodeName === 'A' ||
        mutation.target.nodeName === 'SPAN';
    });

    if (shouldCheck) {
      checkAndHideSidebarItems();
    }
  });

  // Start observing the document
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: false,
    characterData: false
  });

  // Also set up periodic checks
  setInterval(checkAndHideSidebarItems, 2000);
}

// Run the function as soon as the DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', hideSidebarItems);
} else {
  hideSidebarItems();
}

// Also run after window load to catch late-loading elements
window.addEventListener('load', hideSidebarItems);

// Listen for navigation changes (for SPA routing)
window.addEventListener('popstate', () => {
  setTimeout(hideSidebarItems, 100);
});

// Override history methods to catch programmatic navigation
const originalPushState = history.pushState;
const originalReplaceState = history.replaceState;

history.pushState = function (...args) {
  originalPushState.apply(history, args);
  setTimeout(hideSidebarItems, 100);
};

history.replaceState = function (...args) {
  originalReplaceState.apply(history, args);
  setTimeout(hideSidebarItems, 100);
};

// Run again after a delay to catch elements that load after the initial page load
setTimeout(hideSidebarItems, 1000);
setTimeout(hideSidebarItems, 2000);
setTimeout(hideSidebarItems, 5000);
