/**
 * Immediately inject CSS to hide sidebar items
 * This runs as early as possible to prevent flash of unwanted content
 */

import { HIDDEN_SIDEBAR_ITEMS } from "../config/sidebar-config";

// Function to inject CSS immediately
const injectHideSidebarCSS = () => {
  // Create style element
  const style = document.createElement('style');
  style.id = 'hide-sidebar-items-css';
  
  // Generate CSS rules for each item to hide
  const cssRules = HIDDEN_SIDEBAR_ITEMS.map(item => {
    const escapedItem = item.replace(/['"]/g, '\\"');
    return `
      /* Hide ${item} */
      nav li:has(a span:contains("${escapedItem}")),
      aside li:has(a span:contains("${escapedItem}")),
      [role="navigation"] li:has(a span:contains("${escapedItem}")) {
        display: none !important;
        height: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow: hidden !important;
      }
      
      nav a[href*="${item.toLowerCase().replace(/\s+/g, '-')}"] {
        display: none !important;
      }
    `;
  }).join('\n');
  
  // Add fallback CSS for browsers that don't support :has()
  const fallbackCSS = `
    @supports not (selector(:has(*))) {
      .hide-sidebar-item {
        display: none !important;
        height: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow: hidden !important;
      }
    }
  `;
  
  style.textContent = cssRules + fallbackCSS;
  
  // Inject into head
  document.head.appendChild(style);
  
  // Also add JavaScript fallback for browsers without :has() support
  if (!CSS.supports('selector(:has(*))')) {
    addJavaScriptFallback();
  }
};

// JavaScript fallback for browsers without :has() support
const addJavaScriptFallback = () => {
  const hideItems = () => {
    const normalizedItems = HIDDEN_SIDEBAR_ITEMS.map(item => item.toLowerCase());
    
    // Find all navigation links
    const links = document.querySelectorAll('nav a, aside a, [role="navigation"] a');
    
    links.forEach(link => {
      const text = link.textContent?.trim().toLowerCase() || '';
      
      if (normalizedItems.some(item => text === item || text.includes(item))) {
        const listItem = link.closest('li');
        if (listItem) {
          listItem.classList.add('hide-sidebar-item');
        } else {
          link.classList.add('hide-sidebar-item');
        }
      }
    });
  };
  
  // Run immediately and on DOM changes
  hideItems();
  
  const observer = new MutationObserver(hideItems);
  observer.observe(document.body, { childList: true, subtree: true });
};

// Run immediately if DOM is ready, otherwise wait for it
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', injectHideSidebarCSS);
} else {
  injectHideSidebarCSS();
}

// Also run on window load as a backup
window.addEventListener('load', injectHideSidebarCSS);

export { injectHideSidebarCSS };
