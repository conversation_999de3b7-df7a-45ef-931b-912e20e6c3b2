/**
 * CSS to hide sidebar items and remove gaps
 * This prevents the flash of unwanted sidebar items and removes any gaps
 */

/* Initially hide all sidebar navigation to prevent flash */
body.hide-sidebar-loading nav {
  visibility: hidden;
}

/* Once processed, show the navigation again */
body.hide-sidebar-processed nav {
  visibility: visible;
}

/* Target common sidebar structures in Medusa admin */

/* Target list items directly */
nav li:has(a:has(span:contains("Products"))),
nav li:has(a:has(span:contains("Customers"))),
nav li:has(a:has(span:contains("Price Lists"))),
nav li:has(a:has(span:contains("Promotions"))),
nav li:has(a:has(span:contains("Inventory"))),
nav li:has(a:has(span:contains("Orders"))),
nav li:has(a:has(span:contains("Search"))),
nav li:has(a:has(span:contains("Return Reasons"))),
nav li:has(a:has(span:contains("Sales Channels"))),
nav li:has(a:has(span:contains("Product Types"))),
nav li:has(a:has(span:contains("Product Tags"))),
nav li:has(a:has(span:contains("Locations & Shipping"))),
nav li:has(a:has(span:contains("Publishable API Keys"))),
nav li:has(a:has(span:contains("Secret API Keys"))),
nav li:has(a:has(span:contains("Workflows"))),
nav li:has(a:has(span:contains("Webhooks"))) {
  display: none !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

/* Specifically target search elements with various selectors */
nav div:has(input[type="search"]),
nav div:has(input[placeholder*="Search"]),
nav div:has(input[aria-label*="Search"]),
nav div:has(button:has(svg[data-icon="search"])),
nav div:has(svg[data-icon="search"]),
nav div:has(svg[data-testid="search-icon"]),
nav div:has(svg[aria-label*="search"]),
nav div:has(i.search-icon),
nav form[role="search"],
nav .search-container,
nav .search-box,
nav .search-wrapper {
  display: none !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

/* Target any divider elements that might appear between sidebar sections */
nav li:has(a:has(span:contains("Products"))) + li.divider,
nav li:has(a:has(span:contains("Customers"))) + li.divider,
nav li:has(a:has(span:contains("Price Lists"))) + li.divider,
nav li:has(a:has(span:contains("Promotions"))) + li.divider,
nav li:has(a:has(span:contains("Inventory"))) + li.divider,
nav li:has(a:has(span:contains("Orders"))) + li.divider,
nav li:has(a:has(span:contains("Search"))) + li.divider,
nav li:has(a:has(span:contains("Return Reasons"))) + li.divider,
nav li:has(a:has(span:contains("Sales Channels"))) + li.divider,
nav li:has(a:has(span:contains("Product Types"))) + li.divider,
nav li:has(a:has(span:contains("Product Tags"))) + li.divider,
nav li:has(a:has(span:contains("Locations & Shipping"))) + li.divider,
nav li:has(a:has(span:contains("Publishable API Keys"))) + li.divider,
nav li:has(a:has(span:contains("Secret API Keys"))) + li.divider,
nav li:has(a:has(span:contains("Workflows"))) + li.divider,
nav li:has(a:has(span:contains("Webhooks"))) + li.divider {
  display: none !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Target any section headers that might be related to hidden items */
nav div:has(h3:contains("Products")),
nav div:has(h3:contains("Customers")),
nav div:has(h3:contains("Price Lists")),
nav div:has(h3:contains("Promotions")),
nav div:has(h3:contains("Inventory")),
nav div:has(h3:contains("Orders")),
nav div:has(h3:contains("Search")),
nav div:has(h3:contains("Return Reasons")),
nav div:has(h3:contains("Sales Channels")),
nav div:has(h3:contains("Product Types")),
nav div:has(h3:contains("Product Tags")),
nav div:has(h3:contains("Locations & Shipping")),
nav div:has(h3:contains("Developer")),
nav div:has(h3:contains("Extensions")),
nav div:has(h3:contains("Publishable API Keys")),
nav div:has(h3:contains("Secret API Keys")),
nav div:has(h3:contains("Workflows")),
nav div:has(h3:contains("Webhooks")) {
  display: none !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Target any section containers that might contain hidden items */
nav div:has(a:has(span:contains("Products"))),
nav div:has(a:has(span:contains("Customers"))),
nav div:has(a:has(span:contains("Price Lists"))),
nav div:has(a:has(span:contains("Promotions"))),
nav div:has(a:has(span:contains("Inventory"))),
nav div:has(a:has(span:contains("Orders"))),
nav div:has(a:has(span:contains("Search"))),
nav div:has(a:has(span:contains("Return Reasons"))),
nav div:has(a:has(span:contains("Sales Channels"))),
nav div:has(a:has(span:contains("Product Types"))),
nav div:has(a:has(span:contains("Product Tags"))),
nav div:has(a:has(span:contains("Locations & Shipping"))),
nav div:has(a:has(span:contains("Publishable API Keys"))),
nav div:has(a:has(span:contains("Secret API Keys"))),
nav div:has(a:has(span:contains("Workflows"))),
nav div:has(a:has(span:contains("Webhooks"))) {
  display: none !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Fallback for browsers that don't support :has() */
@supports not (selector(:has(*))) {
  /* The JavaScript solution will handle this case */
}
