/**
 * Configuration for sidebar navigation items
 *
 * This file contains the configuration for which sidebar items should be hidden.
 * Modify the HIDDEN_SIDEBAR_ITEMS array to control which items are hidden.
 */

/**
 * Array of sidebar item labels to hide (case-insensitive)
 *
 * Examples:
 * - "Products"
 * - "Customers"
 * - "Orders"
 * - "Settings"
 *
 * You can also use partial matches, which will hide any item containing the text.
 * For example, "Product" will hide both "Products" and "Product Categories".
 */
export const HIDDEN_SIDEBAR_ITEMS: string[] = [
  // Main navigation items to hide
  "Products",
  "Customers",
  "Price Lists",
  "Promotions",
  "Inventory",
  "Orders",
  "Search",
  // Settings sidebar items to hide
  "Return Reasons",
  "Sales Channels",
  "Product Types",
  "Product Tags",
  // "Users", // Commented out to show Users sidebar item
  "Locations & Shipping",
  // Developer-related items
  "Publishable API Keys",
  "Secret API Keys",
  "Workflows",
  "Webhooks", // If present
];

/**
 * Array of group headings to hide (case-insensitive)
 * These are the section headers in the settings sidebar
 */
export const HIDDEN_GROUP_HEADINGS: string[] = [
  "Developer", // Hide the entire Developer section
  "Extensions", // Hide Extensions section if it contains developer tools
  // Add other group headings to hide as needed
];

/**
 * Debug mode - set to true to see console logs
 */
export const DEBUG_MODE = true;

/**
 * Enhanced hiding function that runs multiple times to catch all scenarios
 */
export const runEnhancedHiding = () => {
  console.log("[EnhancedHiding] Starting enhanced hiding process");

  // Hide regular sidebar items
  const hideSidebarItems = () => {
    const normalizedItems = HIDDEN_SIDEBAR_ITEMS.map((item) =>
      item.toLowerCase()
    );
    let hiddenCount = 0;

    // Find all navigation links
    const navLinks = document.querySelectorAll(
      'nav a, aside a, [role="navigation"] a'
    );

    navLinks.forEach((link) => {
      const text = link.textContent?.trim() || "";
      const normalizedText = text.toLowerCase();

      const shouldHide = normalizedItems.some((item) => {
        const normalizedItem = item.toLowerCase();
        return (
          normalizedText === normalizedItem ||
          normalizedText.includes(normalizedItem)
        );
      });

      if (shouldHide) {
        (link as HTMLElement).style.display = "none";
        hiddenCount++;
        console.log(`[EnhancedHiding] Hidden sidebar item: "${text}"`);
      }
    });

    return hiddenCount;
  };

  // Hide group headings
  const hideGroupHeadings = () => {
    const normalizedHeadings = HIDDEN_GROUP_HEADINGS.map((h: string) =>
      h.toLowerCase()
    );
    let hiddenCount = 0;

    // Target the specific structure: <p class="font-normal font-sans txt-compact-small">
    const headingElements = document.querySelectorAll(
      "p.font-normal.font-sans.txt-compact-small"
    );

    headingElements.forEach((element) => {
      const text = element.textContent?.trim().toLowerCase() || "";

      if (normalizedHeadings.includes(text)) {
        // Find the group section to hide
        const groupSection = element.closest('div[data-state="open"].py-3');
        if (groupSection) {
          (groupSection as HTMLElement).style.display = "none";
          hiddenCount++;
          console.log(
            `[EnhancedHiding] Hidden group section: "${element.textContent?.trim()}"`
          );

          // Also hide the separator after this group
          const nextSeparator = groupSection.nextElementSibling;
          if (
            nextSeparator &&
            nextSeparator.querySelector('div[role="separator"]')
          ) {
            (nextSeparator as HTMLElement).style.display = "none";
          }
        }
      }
    });

    return hiddenCount;
  };

  const sidebarItemsHidden = hideSidebarItems();
  const groupHeadingsHidden = hideGroupHeadings();

  console.log(
    `[EnhancedHiding] Hidden ${sidebarItemsHidden} sidebar items and ${groupHeadingsHidden} group headings`
  );

  return { sidebarItemsHidden, groupHeadingsHidden };
};

/**
 * Update this file to change which sidebar items are hidden
 *
 * The changes will take effect when you reload the page.
 *
 * Note: This configuration is used by both the React widget and the direct script injection.
 */
