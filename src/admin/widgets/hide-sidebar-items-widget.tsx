import { defineWidgetConfig } from "@camped-ai/admin-sdk";
import { useEffect, useState, useRef } from "react";
import { hideSidebarItems } from "../utils/hide-sidebar-items";
import { HIDDEN_SIDEBAR_ITEMS } from "../config/sidebar-config";

// Add inline styles for immediate hiding
if (typeof window !== "undefined") {
  // Create and inject a style element
  const style = document.createElement("style");
  style.textContent = `
    /* Initially hide all sidebar navigation to prevent flash */
    body.hide-sidebar-loading nav {
      visibility: hidden;
    }

    /* Once processed, show the navigation again */
    body.hide-sidebar-processed nav {
      visibility: visible;
    }

    /* Hide specific sidebar items based on text content */
    ${HIDDEN_SIDEBAR_ITEMS.map((item) => `nav a span:has-text("${item}")`).join(
      ",\n    "
    )} {
      display: none !important;
    }
  `;
  document.head.appendChild(style);

  // Add a class to the body to hide all navigation initially
  if (document.body) {
    document.body.classList.add("hide-sidebar-loading");
  } else {
    document.addEventListener("DOMContentLoaded", function () {
      document.body.classList.add("hide-sidebar-loading");
    });
  }

  // Create and inject an immediate script
  const script = document.createElement("script");
  script.textContent = `
    // Items to hide from sidebar-config.ts
    const ITEMS_TO_HIDE = ${JSON.stringify(HIDDEN_SIDEBAR_ITEMS)};

    // Function to hide sidebar items
    function hideSidebarItemsEarly() {
      // Convert all items to lowercase for case-insensitive comparison
      const normalizedItemsToHide = ITEMS_TO_HIDE.map(item => item.toLowerCase());

      // Specifically target search elements
      removeSearchElementsEarly();

      // Find all links in the document
      const allLinks = document.querySelectorAll('a');

      // Function to specifically target and remove search elements
      function removeSearchElementsEarly() {
        // Try various selectors that might match search elements
        const searchSelectors = [
          'nav input[type="search"]',
          'nav input[placeholder*="search" i]',
          'nav input[aria-label*="search" i]',
          'nav button:has(svg[data-icon="search"])',
          'nav svg[data-icon="search"]',
          'nav svg[data-testid="search-icon"]',
          'nav svg[aria-label*="search" i]',
          'nav i.search-icon',
          'nav form[role="search"]',
          'nav .search-container',
          'nav .search-box',
          'nav .search-wrapper'
        ];

        // Try each selector
        searchSelectors.forEach(selector => {
          try {
            const elements = document.querySelectorAll(selector);

            elements.forEach(element => {
              // Try to find the most appropriate parent to remove
              const parents = [
                element.closest('li'),
                element.closest('.search-container'),
                element.closest('.search-wrapper'),
                element.closest('form'),
                element.closest('div'),
                element.parentElement
              ].filter(Boolean);

              if (parents.length > 0) {
                // Remove the first (closest) parent that exists
                const parent = parents[0];
                if (parent) {
                  parent.remove();
                }
              } else {
                // If no suitable parent found, remove the element itself
                element.remove();
              }
            });
          } catch (error) {
            console.error('[HideSidebarItems] Error processing search selector:', error);
          }
        });

        // Also try to find elements by text content
        try {
          const allElements = document.querySelectorAll('nav *');
          allElements.forEach(element => {
            const text = element.textContent?.toLowerCase() || '';
            if (text.includes('search')) {
              // Find the appropriate parent to remove
              const parent = element.closest('li') || element.closest('div') || element.parentElement;
              if (parent) {
                parent.remove();
              }
            }
          });
        } catch (error) {
          console.error('[HideSidebarItems] Error searching for text content:', error);
        }
      }

      // Check each link
      allLinks.forEach(link => {
        try {
          // Get the text content of the link
          const linkText = link.textContent?.trim().toLowerCase() || '';

          // Check if this link should be hidden
          const shouldHide = normalizedItemsToHide.some(hideText => {
            return linkText === hideText || linkText.includes(hideText);
          });

          if (shouldHide) {
            // Hide the link immediately
            link.style.display = 'none';
          }
        } catch (error) {
          console.error('[HideSidebarItems] Error processing link:', error);
        }
      });

      // Mark as processed so navigation becomes visible again
      if (document.body) {
        document.body.classList.add('hide-sidebar-processed');
        document.body.classList.remove('hide-sidebar-loading');
      }
    }

    // Run as soon as possible
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', hideSidebarItemsEarly);
    } else {
      hideSidebarItemsEarly();
    }

    // Run again after a short delay
    setTimeout(hideSidebarItemsEarly, 50);
    setTimeout(hideSidebarItemsEarly, 200);
  `;
  script.async = false;
  document.head.appendChild(script);
}

const HideSidebarItemsWidget = () => {
  // Store the observer reference to clean up properly
  const [observer, setObserver] = useState<MutationObserver | null>(null);
  // Track the current URL to detect navigation changes
  const [currentUrl, setCurrentUrl] = useState(window.location.href);

  useEffect(() => {
    // Mark the body as processed to show navigation again
    document.body.classList.add("hide-sidebar-processed");
    document.body.classList.remove("hide-sidebar-loading");

    // Run the hiding logic immediately
    const obs = hideSidebarItems(HIDDEN_SIDEBAR_ITEMS);
    setObserver(obs);

    // Set up a recurring check every 2 seconds
    const intervalId = setInterval(() => {
      // If we already have an observer, disconnect it first
      if (observer) {
        observer.disconnect();
      }
      const newObs = hideSidebarItems(HIDDEN_SIDEBAR_ITEMS);
      setObserver(newObs);
    }, 2000);

    // Listen for URL changes (navigation)
    const handleUrlChange = () => {
      const newUrl = window.location.href;
      if (newUrl !== currentUrl) {
        setCurrentUrl(newUrl);
        // Run hiding logic when URL changes
        setTimeout(() => {
          if (observer) {
            observer.disconnect();
          }
          const newObs = hideSidebarItems(HIDDEN_SIDEBAR_ITEMS);
          setObserver(newObs);
        }, 100); // Small delay to let the new page load
      }
    };

    // Listen for popstate (back/forward navigation)
    window.addEventListener("popstate", handleUrlChange);

    // Listen for pushstate/replacestate (programmatic navigation)
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function (...args) {
      originalPushState.apply(history, args);
      handleUrlChange();
    };

    history.replaceState = function (...args) {
      originalReplaceState.apply(history, args);
      handleUrlChange();
    };

    // Clean up function
    return () => {
      // Clear the interval
      clearInterval(intervalId);

      // Disconnect the observer
      if (observer) {
        observer.disconnect();
      }

      // Remove event listeners
      window.removeEventListener("popstate", handleUrlChange);

      // Restore original history methods
      history.pushState = originalPushState;
      history.replaceState = originalReplaceState;
    };
  }, [observer, currentUrl]);

  // This widget doesn't render anything visible
  return null;
};

// The widget's configurations
// Using common zones that exist in the admin UI
// This ensures our code runs on multiple pages including settings
export const config = defineWidgetConfig({
  zone: [
    "product.list.before",
    "order.list.before",
    "customer.list.before",
    "setting.list.before",
    "settings.list.before",
    "layout.before",
    "layout.after",
  ],
});

export default HideSidebarItemsWidget;
